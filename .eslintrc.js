module.exports = {
    'root': true,
    'plugins': [
        '@typescript-eslint',
        '@stylistic/eslint-plugin-js',
        '@angular-eslint/template',
        'decorator-position'
    ],
    'env': {
        'browser': true,
        'node': true,
        'es6': true
    },
    'ignorePatterns': ['.eslintrc.js'],
    'overrides': [
        {
            'files': [
                '*.html'
            ],
            'extends': [
                'plugin:@angular-eslint/template/recommended',
                'plugin:@angular-eslint/template/process-inline-templates'
            ],
            'parser': '@angular-eslint/template-parser',
            'rules': {
                // '@angular-eslint/template/no-call-expression': [
                //   'error',
                //   {
                //     'allowList': [
                //       'nested',
                //       'getHref'
                //     ]
                //   }
                // ],
                //
                // This will be evailable with Angular 15-16
                // '@angular-eslint/template/prefer-ngsrc': [
                //   'error'
                // ],
                // '@angular-eslint/template/prefer-self-closing-tags': [
                //   'error'
                // ],
                // '@angular-eslint/template/attributes-order': [
                //   'error'
                // ],
                // '@angular-eslint/template/no-inline-styles': [
                //   'error'
                // ],

                '@angular-eslint/template/no-negated-async': [
                    'error'
                ],
                '@angular-eslint/template/no-duplicate-attributes': [
                    'error'
                ],
                '@angular-eslint/template/conditional-complexity': [
                    'error',
                    {
                        'maxComplexity': 3
                    }
                ]
            }
        },
        {
            'files': [
                '*.ts',
                '*.js'
            ],
            'extends': [
                'plugin:@typescript-eslint/recommended',
                'plugin:@angular-eslint/recommended'
            ],
            'parser': '@typescript-eslint/parser',
            'parserOptions': {
                'project': './tsconfig.json'
            },
            'rules': {
                '@stylistic/js/operator-linebreak': [
                    'error',
                    'before'
                ],
                'decorator-position/decorator-position': [
                    'error',
                    {
                        'properties': 'above'
                    }
                ],
                '@typescript-eslint/typedef': [
                    'error',
                    {
                        'parameter': true,
                        'propertyDeclaration': true
                    }
                ],
                '@typescript-eslint/prefer-enum-initializers': 'error',
                '@typescript-eslint/prefer-includes': 'error',
                '@typescript-eslint/prefer-readonly': 'error',
                '@stylistic/js/space-before-blocks': [
                    'error',
                    {
                        'functions': 'always',
                        'keywords': 'always',
                        'classes': 'always'
                    }
                ],
                '@typescript-eslint/type-annotation-spacing': [
                    'error',
                    {
                        'before': false,
                        'after': true,
                        'overrides': {
                            'arrow': {
                                'before': true,
                                'after': true
                            }
                        }
                    }
                ],
                '@typescript-eslint/array-type': 'error',
                '@stylistic/js/arrow-parens': [
                    'error',
                    'always'
                ],
                '@typescript-eslint/no-explicit-any': 'warn',
                '@stylistic/js/comma-spacing': [
                    'error',
                    {
                        'before': false,
                        'after': true
                    }
                ],
                '@stylistic/js/brace-style': 'error',
                '@typescript-eslint/member-ordering': [
                    'error',
                    {
                        'default': {
                            'memberTypes': [
                                // Fields
                                // ["public-static-field", "public-instance-field"]
                                'public-readonly-field',
                                'public-field',
                                // ["protected-static-field", "protected-instance-field"]
                                'protected-field',
                                // ["private-static-field", "private-instance-field"]
                                'private-readonly-field',
                                'private-field',
                                'constructor',
                                // Getters
                                // ["public-static-get", "public-instance-get"]
                                'public-get',
                                // ["protected-static-get", "protected-instance-get"]
                                'protected-get',
                                // ["private-static-get", "private-instance-get"]
                                'private-get',
                                // Setters
                                // ["public-static-set", "public-instance-set"]
                                'public-set',
                                // ["protected-static-set", "protected-instance-set"]
                                'protected-set',
                                // ["private-static-set", "private-instance-set"]
                                'private-set',
                                // Methods
                                // ["public-static-method", "public-instance-method"]
                                'public-method',
                                // ["protected-static-method", "protected-instance-method"]
                                'protected-method',
                                // ["private-static-method", "private-instance-method"]
                                'private-method'
                            ]
                        }
                    }
                ],
                '@typescript-eslint/method-signature-style': [
                    'error',
                    'property'
                ],
                '@typescript-eslint/naming-convention': [
                    'error',
                    {
                        'selector': 'default',
                        'format': [
                            'camelCase'
                        ],
                        'leadingUnderscore': 'allow',
                        'trailingUnderscore': 'allow'
                    },
                    {
                        'selector': 'variable',
                        'format': [
                            'camelCase',
                            'UPPER_CASE'
                        ],
                        'leadingUnderscore': 'forbid',
                        'trailingUnderscore': 'forbid'
                    },
                    {
                        'selector': 'typeLike',
                        'format': [
                            'PascalCase'
                        ]
                    }
                ],
                '@typescript-eslint/consistent-type-definitions': [
                    'error',
                    'type'
                ],
                '@typescript-eslint/consistent-generic-constructors': [
                    'error',
                    'constructor'
                ],
                '@typescript-eslint/lines-between-class-members': [
                    'error',
                    {
                        'enforce': [
                            {
                                'blankLine': 'always',
                                'prev': 'method',
                                'next': 'method'
                            },
                            {
                                'blankLine': 'always',
                                'prev': 'field',
                                'next': 'field'
                            }
                        ]
                    },
                    {
                        'exceptAfterSingleLine': true
                    }
                ],
                '@typescript-eslint/explicit-member-accessibility': [
                    'error',
                    {
                        'ignoredMethodNames': [
                            'constructor'
                        ]
                    }
                ],
                '@typescript-eslint/explicit-function-return-type': [
                    'error',
                    {
                        'allowExpressions': true,
                        'allowConciseArrowFunctionExpressionsStartingWithVoid': true,
                        'allowTypedFunctionExpressions': true,
                        'allowHigherOrderFunctions': true,
                        'allowDirectConstAssertionInArrowFunctions': true
                    }
                ],
                '@typescript-eslint/no-unused-vars': 'error',
                '@typescript-eslint/member-delimiter-style': [
                    'warn',
                    {
                        'multiline': {
                            'delimiter': 'semi',
                            'requireLast': true
                        },
                        'singleline': {
                            'delimiter': 'semi',
                            'requireLast': false
                        }
                    }
                ],
                '@stylistic/js/semi': [
                    'error',
                    'always'
                ],
                '@stylistic/js/quotes': [
                    'error',
                    'single'
                ],
                '@stylistic/js/indent': [
                    'error',
                    4,
                    {
                        'SwitchCase': 1,
                        'ignoreComments': true,
                    }
                ],
                'no-console': 'warn',
                'prefer-const': 'error',
                '@stylistic/js/object-curly-spacing': [
                    'error',
                    'always'
                ]
            }
        }
    ]
};
