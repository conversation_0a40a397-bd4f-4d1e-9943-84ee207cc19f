module.exports = {
    'rules': {
        'attr-bans': [
            true,
            [
                'style'
            ]
        ],
        'attr-new-line': [
            true,
            1
        ],
        'attr-no-dup': true,
        'attr-no-unsafe-char': true,
        'attr-order': [
            true,
            [
                '*ngIf',
                '[ngIf]',
                '*ngFor',
                '[ngFor]',
                '*ngSwitchCase',
                '[ngSwitch]',
                '*ngSwitchDefault',
                'id',
                'class'
            ]
        ],
        'attr-validate': true,
        'class-no-dup': true,
        'id-no-dup': true,
        'id-style': [
            true,
            'dash'
        ],
        'class-style': [
            true,
            'bem'
        ],
        'indent-style': [
            true,
            'spaces'
        ],
        'indent-width': [
            true,
            4
        ],
        'attr-quote-style': [
            true,
            'double'
        ],
        'line-max-len': [
            true,
            120
        ],
        'tag-close': false,
        'spec-char-escape': true,
        'tag-name-lowercase': true,
        'tag-self-close': false,
        'title-no-dup': true
    }
};
