import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import * as Sentry from '@sentry/angular-ivy';
import { BrowserTracing } from '@sentry/tracing';

import { AppModule } from './app/app.module';
import { environment } from '@env/environment';
import { persistState, PersistStateSelectFn } from '@datorama/akita';
import { LandRegistryDialogState } from './app/titles/store/land-registry-dialog';
import { SamUiTourState } from './app/titles/modules/sam-tour/store/sam-ui-tour-store.service';

if (environment.sentryData.isEnabled) {
    let integrationsList = [];
    if (environment.sentryData.isTracingEnabled) {
        integrationsList = [
            new BrowserTracing({
                tracingOrigins: ['localhost', /^\//],
                routingInstrumentation: Sentry.routingInstrumentation,
            }),
        ];
    }
    Sentry.init({
        dsn: environment.sentryData.dsn,
        debug: environment.dev,
        environment: environment.dev ? 'dev' : 'production',
        integrations: integrationsList,

        // Set tracesSampleRate to 1.0 to capture 100%
        // of transactions for performance monitoring.
        // We recommend adjusting this value in production
        tracesSampleRate: environment.sentryData.tracesSampleRate,
    });
}

if (environment.production) {
    enableProdMode();
}

const landRegistry: PersistStateSelectFn<LandRegistryDialogState> = (state) => {
    return {
        registry: state.registry,
    };
};
const samUiTourSelect: PersistStateSelectFn<SamUiTourState> = (state) => {
    return {
        isSeen: state.isSeen,
        finishedSteps: state.finishedSteps,
    };
};
landRegistry.storeName = 'land-registry-dialog';
samUiTourSelect.storeName = 'sam-ui-tour';

enum StoreKeys {
    notice = 'notice',
    landRegistry = 'land-registry',
    samUiTour = 'sam-ui-tour',
}

persistState({
    key: StoreKeys.notice,
    include: ['notice-documents'],
});
persistState({
    key: StoreKeys.landRegistry,
    include: ['land-registry-dialog'],
    select: [landRegistry],
});
persistState({
    key: StoreKeys.samUiTour,
    include: ['sam-ui-tour'],
    select: [samUiTourSelect],
});

export function clearAkitaLocalStorage(): void {
    const keys = Object.values(StoreKeys);
    keys.forEach((key) => {
        localStorage.removeItem(key);
    });
}

platformBrowserDynamic()
    .bootstrapModule(AppModule)
    // eslint-disable-next-line no-console
    .catch((err) => console.error(err));
