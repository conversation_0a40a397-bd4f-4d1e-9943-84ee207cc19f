{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"avail": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "avl", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/shepherd.js/dist/css/shepherd.css", "./node_modules/maplibre-gl/dist/maplibre-gl.css", "src/custom-theme.scss", "src/styles.scss"], "scripts": ["src/assets/js/imanageLib.js"], "aot": true, "optimization": true, "sourceMap": true, "outputHashing": "all", "namedChunks": false, "vendorChunk": false, "buildOptimizer": true, "extractLicenses": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}]}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.production.ts"}]}, "dev": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}]}, "trial": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.trial.ts"}]}, "develop": {"aot": false, "sourceMap": true, "optimization": false, "buildOptimizer": false, "extractLicenses": false, "outputHashing": "none"}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "avail:build:develop", "proxyConfig": "proxy.conf.json"}, "configurations": {"aot": {}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "avail:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "avail:serve"}, "configurations": {"production": {"devServerTarget": "avail:serve:production"}}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "defaultProject": "avail", "cli": {"analytics": false, "defaultCollection": "@angular-eslint/schematics"}}