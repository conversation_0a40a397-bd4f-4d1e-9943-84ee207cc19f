const toxy = require('toxy')

const rules = toxy.rules
const poisons = toxy.poisons

var proxy = toxy()

proxy
  .forward('http://localhost:4200')

proxy
  .post('/api/titles/document/*')
  .rule(rules.probability(75))
  .poison(poisons.inject({
    code: 504,
    body: '{"error": "toxy injected error"}',
    headers: {'Content-Type': 'application/json'}
  }))

proxy
  .post('/api/hmlr/purchase/*')
  .rule(rules.probability(75))
  .poison(poisons.inject({
    code: 402,
    body: '{"error": "toxy injected error"}',
    headers: {'Content-Type': 'application/json'}
  }))

proxy.all('/*')

proxy.listen(8088)
console.log('Server listening on port:', 8088)
