- hosts: all
  tasks:
    - name: Initialize the deploy root and gather facts
      deploy_helper:
        path: '{{ deployPath }}'

    - name: Create log folder in the shared folder
      file:
        path: '{{ deploy_helper.shared_path }}/log'
        state: directory

    - name: Create folder for new release
      file:
        path: '{{ deploy_helper.new_release_path }}'
        state: directory

    - name: Add an unfinished file, to allow cleanup on successful finalize
      file:
        path: '{{ deploy_helper.new_release_path }}/{{ deploy_helper.unfinished_filename }}'
        state: touch

    - name: Extract builded code
      unarchive:
        src: '{{ archivePath }}'
        dest: '{{ deploy_helper.new_release_path }}'

    - name: Add symlink from the new release to the shared folder
      file:
        path: '{{ deploy_helper.new_release_path }}/log'
        src: '{{ deploy_helper.shared_path }}/log'
        state: link

    - name: Finalize the deploy, removing the unfinished file and switching the symlink
      deploy_helper:
        path: '{{ deploy_helper.project_path }}'
        release: '{{ deploy_helper.new_release }}'
        state: finalize
