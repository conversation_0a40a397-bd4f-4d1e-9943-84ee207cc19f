{"name": "avail", "version": "3.9.0", "scripts": {"ng": "ng", "tab:proxy": "ttab -t 'proxy' npm run proxy", "tab:start": "ttab -t 'angular dev server' ng serve", "proxy": "mitmproxy -p 8080 --mode reverse:https://alpha.avail.ai", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint --fix", "lint:html": "npx linthtml 'src/**/*.html'", "lint:scss": "npx prettier --write './**/*.scss'", "e2e": "ng e2e", "analyze": "ng build --stats-json && webpack-bundle-analyzer dist/stats.json", "develop": "ng build --configuration=develop", "dev": "ng build --configuration=dev", "trial": "ng build --configuration=trial", "staging": "ng build --configuration=staging", "production": "ng build --configuration=production", "prepare": "husky install", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org avail-ai --project front-end-dev ./dist && sentry-cli sourcemaps upload --org avail-ai --project front-end-dev ./dist"}, "akitaCli": {"customFolderName": "true", "template": "angular", "basePath": "./src/app/"}, "private": true, "dependencies": {"@angular/animations": "^13.3.11", "@angular/cdk": "^13.3.9", "@angular/common": "^13.3.11", "@angular/compiler": "^13.3.11", "@angular/core": "^13.3.11", "@angular/fire": "^7.2.1", "@angular/forms": "^13.3.11", "@angular/material": "^13.3.9", "@angular/platform-browser": "^13.3.11", "@angular/platform-browser-dynamic": "^13.3.11", "@angular/router": "^13.3.11", "@datorama/akita": "^7.1.1", "@datorama/akita-ng-effects": "^1.0.6", "@sentry/angular-ivy": "^7.114.0", "@sentry/tracing": "^7.114.0", "@turf/turf": "^7.2.0", "angular-shepherd": "^13.0.1", "angular2-promise-buttons": "5.0.3", "core-js": "^2.6.11", "file-saver": "^2.0.5", "firebase": "9.13.0", "lottie-web": "^5.9.6", "maplibre-gl": "3.6.2", "ng-intercom": "^8.0.2", "ngx-cookie-service": "^2.4.0", "ngx-lottie": "~7.1.0", "rxjs": "^7.4.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "zone.js": "~0.11.8"}, "devDependencies": {"@angular-devkit/build-angular": "^13.3.9", "@angular-eslint/builder": "13.5.0", "@angular-eslint/eslint-plugin": "13.5.0", "@angular-eslint/eslint-plugin-template": "13.5.0", "@angular-eslint/schematics": "13.5.0", "@angular-eslint/template-parser": "13.5.0", "@angular/cli": "^13.3.9", "@angular/compiler-cli": "^13.3.11", "@datorama/akita-ngdevtools": "^3.0.2", "@linthtml/linthtml": "^0.9.5", "@stylistic/eslint-plugin-js": "^1.5.1", "@types/file-saver": "^2.0.1", "@types/jasmine": "~2.8.8", "@types/jasminewd2": "^2.0.8", "@types/node": "~14.18.33", "@typescript-eslint/eslint-plugin": "5.62.0", "@typescript-eslint/parser": "5.62.0", "eslint": "8.56.0", "eslint-plugin-decorator-position": "^5.0.2", "husky": "^8.0.0", "jasmine-core": "~2.99.1", "jasmine-spec-reporter": "~4.2.1", "karma": "~6.4.1", "karma-chrome-launcher": "~2.2.0", "karma-coverage-istanbul-reporter": "^2.0.6", "karma-jasmine": "~1.1.2", "karma-jasmine-html-reporter": "^0.2.2", "prettier": "^3.1.1", "protractor": "^7.0.0", "toxy": "^0.3.16", "ts-node": "~7.0.0", "typescript": "4.6.4", "webpack-bundle-analyzer": "^4.8.0"}, "overrides": {"xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz"}}