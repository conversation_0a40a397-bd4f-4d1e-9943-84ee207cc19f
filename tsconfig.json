{
  "compileOnSave": false,
  "compilerOptions": {
    "resolveJsonModule": true,
    "allowSyntheticDefaultImports": true,
    "strictNullChecks": false,
    "skipLibCheck": true,
    "baseUrl": "src",
    "paths": {
      "@env/*": ["environments/*"],
      "@api": ["app/core/api"],
      "@auth/*": ["app/auth/*"],
      "@constants": ["app/core/constants"],
      "@core/types": ["app/core/types"],
      "@enums": ["app/core/enums"],
      "@services": ["app/core/services"],
      "@shared/*": ["app/shared/*"]
    },
    "outDir": "./dist/out-tsc",
    "sourceMap": true,
    "declaration": false,
    "module": "esnext",
    "moduleResolution": "node",
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "importHelpers": true,
    "target": "es2015",
    "typeRoots": [
      "node_modules/@types"
    ],
    "lib": [
      "es2018",
      "dom"
    ]
  },
}
