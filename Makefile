VERSION=$(shell cat package.json | fgrep '"version":' | cut -d '"' -f4  | tr '-' '_')
DEPLOYMENT_DIR:=$(abspath ./../deployments/containers)

.PHONY: prod dev env

prod:
	@echo "Build for production"
	npm run production
	rm -r $(DEPLOYMENT_DIR)/web-ui/real-estate-web/*
	cp -r dist/* $(DEPLOYMENT_DIR)/web-ui/real-estate-web/

dev:
	@echo "Build for dev"
	npm run dev
	rm -r $(DEPLOYMENT_DIR)/web-ui/real-estate-web-dev/*
	cp -r dist/* $(DEPLOYMENT_DIR)/web-ui/real-estate-web-dev/


trial:
	@echo "Build for trial"
	npm run trial
	rm -r $(DEPLOYMENT_DIR)/web-ui/real-estate-web/*
	cp -r dist/* $(DEPLOYMENT_DIR)/web-ui/real-estate-web/

env:
	@echo $(VERSION)
	sed -i "" "s/REAL_ESTATE_WEB_VERSION=.*$\/REAL_ESTATE_WEB_VERSION=$(VERSION)/g" $(DEPLOYMENT_DIR)/.env

env-dev:
	@echo $(VERSION)
	sed -i "" "s/REAL_ESTATE_WEB_DEV_VERSION=.*$\/REAL_ESTATE_WEB_DEV_VERSION=$(VERSION)-dev/g" $(DEPLOYMENT_DIR)/.env
